/**
 * Maps backend error messages to user-friendly messages for display in toasts
 */
export function formatErrorMessage(error: Error): string {
  const message = error.message

  // Map common backend error patterns to user-friendly messages
  const errorMappings: Record<string, string> = {
    'Academic year not found': 'The selected academic year is no longer available. Please refresh and try again.',
    'Adviser not found': 'The selected teacher is no longer available. Please choose a different adviser.',
    'Adviser is not active': 'The selected teacher is currently inactive. Please choose an active teacher.',
    'Track not found': 'The selected track is no longer available. Please choose a different track.',
    'Strand not found': 'The selected strand is no longer available. Please choose a different strand.',
    'Major not found': 'The selected major is no longer available. Please choose a different major.',
    'Strand does not belong to the specified track': 'The selected strand is not compatible with the chosen track. Please select a valid combination.',
    'Major does not belong to the specified strand': 'The selected major is not compatible with the chosen strand. Please select a valid combination.',
    'A section with this name already exists for this grade level and academic year': 'A section with this name already exists for this grade level and academic year. Please choose a different name.',
    'Section not found': 'This section could not be found. It may have been deleted by another user.',
    'Student counts must be non-negative': 'Student counts cannot be negative numbers. Please enter valid counts.',
  }

  // Check for exact matches first
  if (errorMappings[message]) {
    return errorMappings[message]
  }

  // Check for partial matches for more flexible error handling
  for (const [pattern, friendlyMessage] of Object.entries(errorMappings)) {
    if (message.includes(pattern)) {
      return friendlyMessage
    }
  }

  // Fallback for unknown errors - make them slightly more user-friendly
  if (message.toLowerCase().includes('not found')) {
    return 'The requested item could not be found. Please refresh and try again.'
  }

  if (message.toLowerCase().includes('already exists')) {
    return 'This item already exists. Please choose a different name or value.'
  }

  if (message.toLowerCase().includes('invalid') || message.toLowerCase().includes('validation')) {
    return 'Please check your input and try again.'
  }

  // If no mapping found, return the original message but clean it up
  return message || 'An unexpected error occurred. Please try again.'
}
